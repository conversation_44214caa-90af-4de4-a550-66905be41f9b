<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cashop.aggr</groupId>
        <artifactId>cashop-aggr-prod</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>cashop-aggr-prod-service</artifactId>
    <packaging>jar</packaging>

    <name>cashop-aggr-prod-service</name>
    <description>业务服务层：业务逻辑实现</description>

    <dependencies>
        <!-- common模块 -->
        <dependency>
            <groupId>com.cashop.aggr</groupId>
            <artifactId>cashop-aggr-prod-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- dao模块 -->
        <dependency>
            <groupId>com.cashop.aggr</groupId>
            <artifactId>cashop-aggr-prod-dao</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring IOC基础包 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <!-- SLF4J -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <!-- Log4j2 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
    </dependencies>
</project>
