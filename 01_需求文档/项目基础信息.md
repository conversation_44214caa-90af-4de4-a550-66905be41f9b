# 项目基础信息

## 项目概述
- **项目名称**: cashop-aggr-prod
- **项目描述**: 基于Spring Boot 3.2.6的Java多模块微服务项目
- **技术架构**: Maven多模块 + Spring Boot + MyBatis + Eureka

## 项目人员
- **开发人员**: 待定
- **项目周期**: 待定
- **预估工时**: 待定

## 功能概述
- 微服务架构的聚合服务
- 提供RESTful API接口
- 支持服务注册与发现
- 集成配置中心Apollo
- 完整的日志管理体系

## 技术架构
- **后端框架**: Spring Boot 3.2.6
- **服务发现**: Eureka
- **配置中心**: Apollo 2.1.0
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis 3.5.14
- **日志框架**: Log4j2 2.21.1
- **API文档**: OpenAPI 3.0

## 依赖项目
- 无外部项目依赖

## 开发工具
- **JDK**: OpenJDK 17
- **构建工具**: Maven 3.6+
- **IDE**: IntelliJ IDEA / Eclipse
- **版本控制**: Git

## 部署环境
- **开发环境**: stable
- **测试环境**: release  
- **生产环境**: prod
- **本地环境**: local
