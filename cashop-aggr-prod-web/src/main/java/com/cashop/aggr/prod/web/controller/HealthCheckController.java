package com.cashop.aggr.prod.web.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@Tag(name = "健康检查", description = "系统健康检查相关接口")
public class HealthCheckController {

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @Operation(summary = "健康检查", description = "检查系统健康状态，返回ok表示系统正常")
    @GetMapping(value = "/slb/health")
    public String health() {
        return "ok";
    }
}
