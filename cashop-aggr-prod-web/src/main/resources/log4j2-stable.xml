<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <!-- 控制台输出 - 非本地环境注释掉 -->
        <!--
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}[%-5level][%t][%logger{36}][%L] - %msg%n"/>
        </Console>
        -->
        
        <!-- 文件输出 -->
        <RollingFile name="RollingFile" fileName="/usr/local/tomcat/logs/catalina.out"
                     filePattern="/usr/local/tomcat/logs/catalina-%d{yyyy-MM-dd}.out">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}[%-5level][%t][%logger{36}][%L] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>
    </Appenders>
    
    <Loggers>
        <!-- 项目包日志级别 -->
        <Logger name="com.cashop" level="INFO" additivity="false">
            <AppenderRef ref="RollingFile"/>
        </Logger>
        <Logger name="com.mengxiang" level="INFO" additivity="false">
            <AppenderRef ref="RollingFile"/>
        </Logger>
        <Logger name="com.aikucun" level="INFO" additivity="false">
            <AppenderRef ref="RollingFile"/>
        </Logger>
        
        <!-- 根日志级别 -->
        <Root level="WARN">
            <AppenderRef ref="RollingFile"/>
        </Root>
    </Loggers>
</Configuration>
